import React from 'react';
import { motion } from 'framer-motion';
import Typography from '../ui/Typography';

const CredibilityBar: React.FC = () => {
  const credentials = [
    { name: 'Better Business Bureau', rating: 'A+', logo: '🏆' },
    { name: 'Licensed & Insured', detail: 'Fully Bonded', logo: '🛡️' },
    { name: 'EPA Certified', detail: 'Lead-Safe', logo: '✅' },
    { name: 'OSHA Compliant', detail: 'Safety First', logo: '⚡' },
    { name: 'Warranty Backed', detail: '5-Year Guarantee', logo: '🎯' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-16 bg-white border-b border-neutral-100">
      <div className="container mx-auto px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <Typography variant="caption" className="text-neutral-500 uppercase tracking-wider mb-2">
            Trusted by Leading Businesses
          </Typography>
          <Typography variant="title" className="text-neutral-800">
            Certified Excellence You Can Count On
          </Typography>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 md:grid-cols-5 gap-8 items-center"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {credentials.map((credential, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="text-center group cursor-pointer"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-6 mb-3 group-hover:shadow-xl transition-all duration-300">
                <div className="text-3xl mb-2">{credential.logo}</div>
                <Typography variant="caption" className="font-semibold text-neutral-800 mb-1">
                  {credential.name}
                </Typography>
                <Typography variant="caption" className="text-primary-600">
                  {credential.rating || credential.detail}
                </Typography>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Client Logos Section */}
        <motion.div
          className="mt-16 pt-12 border-t border-neutral-100"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <Typography variant="caption" className="text-center text-neutral-500 uppercase tracking-wider mb-8">
            Trusted by Industry Leaders
          </Typography>
          
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {/* Placeholder for client logos - replace with actual logos */}
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <motion.div
                key={item}
                className="bg-neutral-200 rounded-lg px-8 py-4 text-neutral-400 font-medium"
                whileHover={{ opacity: 1, scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                Client Logo {item}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CredibilityBar;
