import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'luxury' | 'glass';
  className?: string;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  className = '',
  hover = true,
}) => {
  const baseClasses = 'rounded-2xl transition-all duration-500';
  
  const variantClasses = {
    default: 'bg-white border border-neutral-200 shadow-lg',
    luxury: 'bg-white/80 backdrop-blur-sm border border-white/30 shadow-xl',
    glass: 'bg-white/10 backdrop-blur-md border border-white/20',
  };

  const hoverClasses = hover ? 'hover:shadow-2xl hover:-translate-y-2' : '';

  return (
    <motion.div
      className={`${baseClasses} ${variantClasses[variant]} ${hoverClasses} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      whileHover={hover ? { y: -8 } : {}}
    >
      {children}
    </motion.div>
  );
};

export default Card;
