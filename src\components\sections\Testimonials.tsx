import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';
import Card from '../ui/Card';
import Typography from '../ui/Typography';

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      title: 'CEO, TechFlow Solutions',
      company: 'Fortune 500 Technology Company',
      content: 'ATM Painting transformed our corporate headquarters beyond our expectations. Their attention to detail and commitment to minimal business disruption was exceptional. The result speaks volumes about our company\'s commitment to excellence.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: '<PERSON>',
      title: 'Property Manager',
      company: 'Premier Commercial Real Estate',
      content: 'Working with ATM Painting has been a game-changer for our portfolio. Their expertise in commercial coatings and ability to work within tight schedules has saved us countless hours and exceeded tenant expectations.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      name: '<PERSON>',
      title: 'Facilities Director',
      company: 'Metropolitan Medical Center',
      content: 'The team\'s professionalism and use of low-VOC, health-conscious materials made them the perfect choice for our medical facility. The quality of work and attention to safety protocols was outstanding.',
      rating: 5,
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-24 bg-neutral-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Typography variant="display" as="h2" className="mb-6 text-white">
            Trusted by Industry Leaders
          </Typography>
          <Typography variant="body-lg" className="text-neutral-300">
            Don't just take our word for it. Here's what our clients say about 
            the transformative impact of our commercial painting services.
          </Typography>
        </motion.div>

        {/* Testimonials Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {testimonials.map((testimonial, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card variant="glass" className="h-full p-8 relative">
                {/* Quote Icon */}
                <div className="absolute top-6 right-6 opacity-20">
                  <Quote className="w-8 h-8 text-white" />
                </div>

                {/* Rating */}
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, starIndex) => (
                    <Star
                      key={starIndex}
                      className="w-5 h-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>

                {/* Content */}
                <Typography variant="body" className="text-white mb-8 leading-relaxed">
                  "{testimonial.content}"
                </Typography>

                {/* Author */}
                <div className="flex items-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover mr-4"
                  />
                  <div>
                    <Typography variant="body" className="font-semibold text-white mb-1">
                      {testimonial.name}
                    </Typography>
                    <Typography variant="caption" className="text-neutral-300">
                      {testimonial.title}
                    </Typography>
                    <Typography variant="caption" className="text-neutral-400 block">
                      {testimonial.company}
                    </Typography>
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          {[
            { number: '500+', label: 'Projects Completed' },
            { number: '98%', label: 'Client Satisfaction' },
            { number: '15+', label: 'Years Experience' },
            { number: '24/7', label: 'Support Available' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              className="text-center"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Typography variant="display" className="text-primary-400 mb-2">
                {stat.number}
              </Typography>
              <Typography variant="caption" className="text-neutral-300 uppercase tracking-wider">
                {stat.label}
              </Typography>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
