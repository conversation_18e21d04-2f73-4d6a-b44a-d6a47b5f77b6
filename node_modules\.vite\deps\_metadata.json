{"hash": "fe670efd", "configHash": "bde0df04", "lockfileHash": "6b7ff55c", "browserHash": "9fb2171b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "103384b7", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "27c19acb", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "33ca01cf", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b905bbf8", "needsInterop": true}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "714e0b19", "needsInterop": false}, "lenis": {"src": "../../lenis/dist/lenis.mjs", "file": "lenis.js", "fileHash": "41556841", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "7cf9311f", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "670c41dc", "needsInterop": true}}, "chunks": {"chunk-HE4GKDYE": {"file": "chunk-HE4GKDYE.js"}, "chunk-MJNCUEZK": {"file": "chunk-MJNCUEZK.js"}, "chunk-UGC3UZ7L": {"file": "chunk-UGC3UZ7L.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}