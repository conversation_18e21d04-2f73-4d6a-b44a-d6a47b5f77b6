import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export const useGSAP = () => {
  const contextRef = useRef<gsap.Context>();

  useEffect(() => {
    contextRef.current = gsap.context(() => {});
    
    return () => {
      contextRef.current?.revert();
    };
  }, []);

  return contextRef.current;
};

export const useScrollAnimation = (
  trigger: string,
  animation: gsap.TweenVars,
  options?: ScrollTrigger.Vars
) => {
  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(trigger, 
        { opacity: 0, y: 50 },
        {
          ...animation,
          scrollTrigger: {
            trigger,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
            ...options,
          },
        }
      );
    });

    return () => ctx.revert();
  }, [trigger, animation, options]);
};
