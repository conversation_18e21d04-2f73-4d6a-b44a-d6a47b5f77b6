import React from 'react';
import { motion } from 'framer-motion';

interface TypographyProps {
  children: React.ReactNode;
  variant?: 'hero' | 'display' | 'headline' | 'title' | 'body-lg' | 'body' | 'caption';
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  className?: string;
  gradient?: boolean;
  animate?: boolean;
}

const Typography: React.FC<TypographyProps> = ({
  children,
  variant = 'body',
  as: Component = 'p',
  className = '',
  gradient = false,
  animate = false,
}) => {
  const variantClasses = {
    hero: 'text-hero font-serif font-bold tracking-tight',
    display: 'text-display font-serif font-semibold tracking-tight',
    headline: 'text-headline font-serif font-semibold',
    title: 'text-title font-sans font-semibold',
    'body-lg': 'text-body-lg font-sans',
    body: 'text-body font-sans',
    caption: 'text-caption font-sans text-neutral-600',
  };
  
  const gradientClass = gradient ? 'text-gradient-primary' : '';
  
  const content = (
    <Component className={`${variantClasses[variant]} ${gradientClass} ${className}`}>
      {children}
    </Component>
  );
  
  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {content}
      </motion.div>
    );
  }
  
  return content;
};

export default Typography;
