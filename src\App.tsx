import React, { useState } from 'react';
import { useLenis } from './hooks/useLenis';
import LoadingScreen from './components/ui/LoadingScreen';
import Hero from './components/sections/Hero';
import CredibilityBar from './components/sections/CredibilityBar';
import Services from './components/sections/Services';
import Testimonials from './components/sections/Testimonials';
import Contact from './components/sections/Contact';
import Footer from './components/sections/Footer';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  // Initialize smooth scrolling
  useLenis();

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  return (
    <>
      {isLoading && <LoadingScreen onComplete={handleLoadingComplete} />}
      <div className="min-h-screen bg-luxury-cream">
        <Hero />
        <CredibilityBar />
        <Services />
        <Testimonials />
        <Contact />
        <Footer />
      </div>
    </>
  );
}

export default App;
