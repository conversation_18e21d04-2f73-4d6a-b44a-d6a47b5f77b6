@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import luxury fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* Custom luxury styles */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-luxury-cream text-neutral-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Luxury button styles */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-luxury hover:shadow-luxury-lg;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 rounded-lg font-medium transition-all duration-300;
  }

  /* Glassmorphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Luxury card */
  .luxury-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-luxury hover:shadow-luxury-lg transition-all duration-500 transform hover:-translate-y-2;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary-400 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

@layer utilities {
  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }

  .text-gradient-luxury {
    @apply bg-gradient-to-r from-luxury-bronze to-luxury-silver bg-clip-text text-transparent;
  }

  /* Magnetic effect for buttons */
  .magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .magnetic:hover {
    transform: scale(1.05);
  }
}
