/* Import luxury fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";

@theme {
  /* Mexican flag inspired luxury palette */
  --color-primary-50: oklch(0.98 0.02 142);
  --color-primary-100: oklch(0.95 0.05 142);
  --color-primary-200: oklch(0.90 0.10 142);
  --color-primary-300: oklch(0.82 0.15 142);
  --color-primary-400: oklch(0.72 0.20 142);
  --color-primary-500: oklch(0.62 0.25 142);
  --color-primary-600: oklch(0.52 0.22 142);
  --color-primary-700: oklch(0.42 0.18 142);
  --color-primary-800: oklch(0.32 0.14 142);
  --color-primary-900: oklch(0.22 0.10 142);
  --color-primary-950: oklch(0.12 0.06 142);

  --color-accent-50: oklch(0.97 0.02 25);
  --color-accent-100: oklch(0.93 0.05 25);
  --color-accent-200: oklch(0.87 0.10 25);
  --color-accent-300: oklch(0.78 0.15 25);
  --color-accent-400: oklch(0.68 0.20 25);
  --color-accent-500: oklch(0.58 0.25 25);
  --color-accent-600: oklch(0.48 0.22 25);
  --color-accent-700: oklch(0.38 0.18 25);
  --color-accent-800: oklch(0.28 0.14 25);
  --color-accent-900: oklch(0.18 0.10 25);
  --color-accent-950: oklch(0.08 0.06 25);

  --color-luxury-cream: oklch(0.98 0.01 85);
  --color-luxury-bronze: oklch(0.55 0.08 45);
  --color-luxury-silver: oklch(0.75 0.02 0);
  --color-luxury-charcoal: oklch(0.25 0.02 240);
  --color-luxury-navy: oklch(0.20 0.08 240);

  /* Typography */
  --font-serif: "Playfair Display", serif;
  --font-sans: "Inter", sans-serif;

  /* Custom shadows */
  --shadow-luxury: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-luxury-lg: 0 35px 60px -12px rgb(0 0 0 / 0.3);
}

/* Custom luxury styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-luxury-cream text-neutral-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Improve performance for animations */
  * {
    will-change: auto;
  }

  /* Optimize images */
  img {
    @apply max-w-full h-auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

@layer components {
  /* Luxury button styles */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-4 rounded-lg font-medium transition-all duration-300;
  }

  /* Glassmorphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* Luxury card */
  .luxury-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/30 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary-400 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

@layer utilities {
  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }

  .text-gradient-luxury {
    @apply bg-gradient-to-r from-luxury-bronze to-luxury-silver bg-clip-text text-transparent;
  }

  /* Magnetic effect for buttons */
  .magnetic {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .magnetic:hover {
    transform: scale(1.05);
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Responsive text sizing */
  .text-responsive {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  .text-responsive-xl {
    font-size: clamp(2rem, 6vw, 4.5rem);
  }

  /* Container utilities */
  .container-padding {
    @apply px-6 lg:px-8;
  }

  /* Focus states for accessibility */
  .focus-luxury:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
}
