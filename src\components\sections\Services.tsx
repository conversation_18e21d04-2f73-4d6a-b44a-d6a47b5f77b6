import React from 'react';
import { motion } from 'framer-motion';
import { Paintbrush, Building, Shield, Clock } from 'lucide-react';
import Card from '../ui/Card';
import Typography from '../ui/Typography';
import Button from '../ui/Button';

const Services: React.FC = () => {
  const services = [
    {
      icon: Building,
      title: 'Commercial Exteriors',
      description: 'Transform your business facade with premium exterior painting that commands attention and builds trust.',
      features: ['Weather-resistant coatings', 'Color consultation', '10-year warranty'],
      image: 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=600&h=400&fit=crop'
    },
    {
      icon: Paintbrush,
      title: 'Interior Transformations',
      description: 'Create inspiring workspaces that enhance productivity and reflect your brand values.',
      features: ['Low-VOC paints', 'Minimal disruption', 'Custom finishes'],
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop'
    },
    {
      icon: Shield,
      title: 'Protective Coatings',
      description: 'Industrial-grade protective solutions that extend the life of your commercial property.',
      features: ['Anti-corrosion treatment', 'Fire-resistant options', 'Specialized applications'],
      image: 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=600&h=400&fit=crop'
    },
    {
      icon: Clock,
      title: 'Emergency Services',
      description: '24/7 rapid response for urgent painting needs that can\'t wait for business hours.',
      features: ['Same-day quotes', 'Weekend availability', 'Emergency repairs'],
      image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=600&h=400&fit=crop'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="py-24 bg-gradient-to-b from-white to-neutral-50">
      <div className="container mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Typography variant="display" as="h2" className="mb-6 text-neutral-900">
            Premium Commercial Painting Services
          </Typography>
          <Typography variant="body-lg" className="text-neutral-600">
            From corporate headquarters to retail spaces, we deliver exceptional results 
            that elevate your brand and protect your investment.
          </Typography>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {services.map((service, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card variant="luxury" className="h-full overflow-hidden group">
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-4 left-4">
                    <div className="bg-primary-600 p-3 rounded-lg">
                      <service.icon className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
                
                <div className="p-8">
                  <Typography variant="title" as="h3" className="mb-4 text-neutral-900">
                    {service.title}
                  </Typography>
                  <Typography variant="body" className="text-neutral-600 mb-6">
                    {service.description}
                  </Typography>
                  
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-neutral-700">
                        <div className="w-2 h-2 bg-primary-500 rounded-full mr-3" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button variant="secondary" size="sm" className="w-full">
                    Learn More
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Process Section */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Typography variant="headline" as="h3" className="mb-8 text-neutral-900">
            Our Proven Process
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {[
              { step: '01', title: 'Consultation', desc: 'Detailed assessment and custom proposal' },
              { step: '02', title: 'Preparation', desc: 'Meticulous surface prep and protection' },
              { step: '03', title: 'Application', desc: 'Expert application with premium materials' },
              { step: '04', title: 'Inspection', desc: 'Quality assurance and final walkthrough' }
            ].map((process, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Typography variant="title" className="text-primary-600 font-bold">
                    {process.step}
                  </Typography>
                </div>
                <Typography variant="body" className="font-semibold text-neutral-900 mb-2">
                  {process.title}
                </Typography>
                <Typography variant="caption" className="text-neutral-600">
                  {process.desc}
                </Typography>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
