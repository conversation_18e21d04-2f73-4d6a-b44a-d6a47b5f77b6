import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Phone, Mail } from 'lucide-react';
import Button from '../ui/Button';
import Typography from '../ui/Typography';

const Hero: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-luxury-cream via-white to-primary-50">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-20 left-20 w-32 h-32 bg-primary-200 rounded-full opacity-20"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 180, 360],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute bottom-20 right-20 w-24 h-24 bg-accent-200 rounded-full opacity-20"
        animate={{
          y: [0, 20, 0],
          rotate: [360, 180, 0],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Badge */}
          <motion.div variants={itemVariants} className="mb-8">
            <span className="inline-flex items-center px-6 py-2 rounded-full bg-primary-100 text-primary-700 text-sm font-medium border border-primary-200">
              🏆 Award-Winning Commercial Painting Excellence
            </span>
          </motion.div>

          {/* Main Headline */}
          <motion.div variants={itemVariants}>
            <Typography
              variant="hero"
              as="h1"
              className="mb-6 text-neutral-900 leading-tight"
            >
              Transform Your Business with{' '}
              <span className="text-gradient-primary">
                Luxury Commercial Painting
              </span>
            </Typography>
          </motion.div>

          {/* Subheadline */}
          <motion.div variants={itemVariants}>
            <Typography
              variant="body-lg"
              className="mb-8 text-neutral-600 max-w-2xl mx-auto leading-relaxed"
            >
              ATM Painting delivers world-class commercial painting services that elevate your brand, 
              inspire confidence, and create lasting impressions. Experience the difference of true craftsmanship.
            </Typography>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Button
              variant="primary"
              size="lg"
              icon={Phone}
              iconPosition="left"
              className="w-full sm:w-auto"
            >
              Schedule Consultation
            </Button>
            <Button
              variant="secondary"
              size="lg"
              icon={ArrowRight}
              iconPosition="right"
              className="w-full sm:w-auto"
            >
              View Our Portfolio
            </Button>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div className="text-center">
              <Typography variant="display" as="div" className="text-primary-600 mb-2">
                500+
              </Typography>
              <Typography variant="caption" className="text-neutral-600">
                Projects Completed
              </Typography>
            </div>
            <div className="text-center">
              <Typography variant="display" as="div" className="text-primary-600 mb-2">
                15+
              </Typography>
              <Typography variant="caption" className="text-neutral-600">
                Years of Excellence
              </Typography>
            </div>
            <div className="text-center">
              <Typography variant="display" as="div" className="text-primary-600 mb-2">
                100%
              </Typography>
              <Typography variant="caption" className="text-neutral-600">
                Client Satisfaction
              </Typography>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-neutral-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-neutral-400 rounded-full mt-2"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
